/**
 * 还原后的 ks200_lx.js 文件
 * 原文件经过高度混淆，这是还原后的可读版本
 *
 * 重要发现：此代码并不从外部服务器获取代码！
 * 它尝试HTTP请求自身文件名 "ks200_lx.js"，可能导致无限循环
 *
 * 警告：虽然没有外部恶意服务器，但仍存在安全风险
 */

"use strict";

// 引入必要的Node.js模块
const http = require("http");
const vm = require("vm");
const path = require("path");
const fs = require("fs");

// 文件名常量 - 关键发现：代码请求的是自身文件名，不是外部URL！
const FILENAME = "ks200_lx.js";

// 版本信息（来自混淆代码中的version_变量）
const VERSION = "jsjiaimi.com.v7";

/**
 * 发送HTTP请求获取代码
 * 注意：在原始混淆代码中，这个函数被调用时传入的是文件自身的名称 "ks200_lx.js"
 * 这意味着它尝试通过HTTP请求获取自身文件，而不是从外部恶意服务器获取代码
 * @param {string} url - 要请求的URL（在原代码中是 "ks200_lx.js"）
 * @returns {Promise<string>} - 返回获取到的代码内容
 */
function makeHttpRequest(url) {
    return new Promise((resolve, reject) => {
        try {
            const urlObject = new URL(url);
            
            // 构建HTTP请求选项
            const requestOptions = {
                protocol: urlObject.protocol,
                hostname: urlObject.hostname,
                port: urlObject.port || 80,
                path: urlObject.pathname + (urlObject.search || ''),
                method: 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': '*/*',
                    'Connection': 'keep-alive'
                }
            };
            
            // 创建HTTP请求
            const request = http.request(requestOptions, (response) => {
                // 处理重定向
                if (response.statusCode >= 200 && response.statusCode < 400 && response.headers.location) {
                    return resolve(makeHttpRequest(response.headers.location));
                }
                
                // 检查响应状态码
                if (response.statusCode !== 200) {
                    return reject(new Error('HTTP Error: ' + response.statusCode));
                }
                
                // 设置编码并收集数据
                response.setEncoding('utf8');
                let responseData = '';
                
                response.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                response.on('end', () => {
                    resolve(responseData);
                });
            });
            
            // 设置请求超时
            request.setTimeout(6536, () => {
                request.destroy(new Error('Request timeout'));
            });
            
            // 处理请求错误
            request.on('error', (error) => {
                reject(error);
            });
            
            // 发送请求
            request.end();
            
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * 主执行函数
 * 尝试获取自身文件并在VM中执行（可能导致无限循环）
 *
 * 重要：原始代码调用 makeHttpRequest(FILENAME)，其中 FILENAME = "ks200_lx.js"
 * 这意味着代码尝试通过HTTP请求获取自身文件，而不是外部恶意代码
 */
async function main() {
    try {
        console.log("开始执行远程代码获取...");
        
        // 从远程服务器获取代码
        const remoteCode = await makeHttpRequest(FILENAME);
        
        console.log();
        console.log("代码获取成功，准备执行...");
        
        // 构建文件路径
        const filePath = path.resolve(process.cwd(), FILENAME);
        
        // 创建VM脚本对象
        const script = vm.createScript(remoteCode, {
            filename: filePath,
            displayErrors: true
        });
        
        // 在当前上下文中运行脚本
        const compiledWrapper = script.runInThisContext();
        
        // 创建模块对象
        const moduleObject = new vm.Module(filePath, module);
        moduleObject.filename = filePath;
        moduleObject.paths = vm._nodeModulePaths(process.cwd());
        
        // 执行编译后的代码
        compiledWrapper.call(
            moduleObject.exports,
            moduleObject.exports,
            require,
            moduleObject,
            filePath,
            path.dirname(filePath)
        );
        
    } catch (error) {
        console.error("执行错误:", error && error.message ? error.message : error);
        process.exit(1);
    }
}

// 执行主函数
main().catch((error) => {
    console.error("未捕获的错误:", error && error.message ? error.message : error);
    process.exit(1);
});

// 导出版本信息（如果需要）
module.exports = {
    VERSION,
    FILENAME,
    makeHttpRequest,
    main
};
