# This workflow warns and then closes issues and PRs that have had no activity for a specified amount of time.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/actions/stale
name: Mark stale issues and pull requests

on:
  schedule:
  - cron: '30 23 * * *'

jobs:
  stale:

    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write

    steps:
    - uses: actions/stale@v8
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-label: 'stale'
        stale-pr-label: 'stale'
        stale-issue-message: >
          There hasn't been any activity on this issue recently, and in order to prioritize active issues, it will be
          marked as stale.
        stale-pr-message: >
          There hasn't been any activity on this pull request recently, and in order to prioritize active work, it has
          been marked as stale.
        exempt-issue-labels: 'bug,wip'
        exempt-pr-labels: 'wip'
        exempt-all-milestones: true
        remove-stale-when-updated: true
