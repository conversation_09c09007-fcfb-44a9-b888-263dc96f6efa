name: "🐛 Bug Report"
description: "If something isn't working as expected 🤔."
title: "[Bug]: "
body:
  - type: markdown
    attributes:
      value: Thanks for taking the time to file a bug report! Please fill out this form as completely as possible.

  - type: dropdown
    attributes:
      label: Which plugin are you reporting a bug for?
      options:
        - "obfuscator"
        - "sojson"
        - "sojsonv7"
        - "visitor"
        - "Other"
    validations:
      required: true

  - type: textarea
    attributes:
      label: Version of Node
      description: |
        **Tip:** you can run `node -v` and paste the result below
      placeholder: |
        - Node: [e.g. v18.20.4]
    validations:
      required: true

  - type: textarea
    attributes:
      label: Input code
      description: |
        You can write here the minimal input code necessary to reproduce the bug. Or share a link.
      placeholder: |
        ```js
        var your => (code) => here;
        ```
    validations:
      required: true

  - type: textarea
    attributes:
      label: Additional context
      description: "Add any other context about the problem here. Or a screenshot if applicable."
