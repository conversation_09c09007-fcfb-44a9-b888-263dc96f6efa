{"name": "decode-js", "type": "module", "scripts": {"decode": "node src/main.js", "deob": "node src/main.js -t obfuscator", "deso": "node src/main.js -t sojson", "desov7": "node src/main.js -t sojsonv7", "test": "vitest --config vitest.config.js", "lint": "eslint --ext .js --fix src"}, "dependencies": {"@babel/generator": "^7.25.0", "@babel/parser": "^7.25.0", "@babel/traverse": "^7.25.2", "@babel/types": "^7.25.0", "eslint": "^8.23.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "isolated-vm": "^5.0.4", "prettier": "^2.7.1"}, "devDependencies": {"@vitest/coverage-v8": "^3.0.5", "vitest": "^3.0.5"}}